// src/main.js
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import router from './router'
import request from '@/utils/request'
import axios from 'axios';

// 创建应用
const app = createApp(App)
const pinia = createPinia();

// 安装插件
app.use(pinia)
app.use(ElementPlus)
app.use(router)

// 全局属性
app.config.globalProperties.$request = request
app.config.globalProperties.$http = axios;

// 全局混入
app.mixin({
  created() { /* ... */ },
});

// 提供store实例
import { useMainStore } from './store';
const store = useMainStore();
app.provide('store', store);

// 挂载应用
app.mount('#app')