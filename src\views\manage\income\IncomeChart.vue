<!--  -->
<template>
  <div class="income-chart-container">
    <el-tabs v-model="activeName" @tab-click="handleClick" class="chart-tabs">
      <div class="total-display">
        总计：￥{{ total}}
      </div>

      <!--      柱状图-->
      <el-tab-pane label="各类收入柱状图" name="bar">
        <div id="bar" class="chart-container"></div>
      </el-tab-pane>

      <!--      饼图-->
      <el-tab-pane label="各类收入饼图" name="pie">
        <div id="pie" class="chart-container pie-chart"></div>
      </el-tab-pane>

      <!--  本周收入折线图-->
      <el-tab-pane label="本周收入" name="line1">
        <div id="weekLine" class="chart-container line-chart"></div>
      </el-tab-pane>

      <!-- 本月收入折线图-->
      <el-tab-pane label="本月收入" name="line2">
        <div id="monthLine" class="chart-container month-chart"></div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "IncomeChart", // ✅ 推荐使用多词组件名

  // 引入的组件需要注册
  components: {},

  // 数据区
  data() {
    return {
      activeName: "bar",
      sumIncome: 0,
      categoryIncomes: [],
      categoryNames: [],
      incomes: [],
      totalAll: 0,
      totalWeek: 0,
      totalMonth: 0,
      total: 0,
    };
  },

  // 计算属性
  computed: {},

  // 监听数据变化
  watch: {},

  // 方法集合
  methods: {
    handleClick(tab) {
      switch (tab.name) {
        case "bar":
          this.total = this.totalAll;
          this.initBarChart();
          break;
        case "pie":
          this.total = this.totalAll;
          this.initPieChart();
          break;
        case "line1":
          this.total = this.totalWeek;
          this.initWeekLineChart();
          break;
        case "line2":
          this.total = this.totalMonth;
          this.initMonthLineChart();
          break;
      }
    },

    // 初始化柱状图
    initBarChart() {
      this.$nextTick(() => {
        const barChart = echarts.init(document.getElementById("bar"));
        const barOption = {
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: 'shadow'
            }
          },
          title: {
            text: "本周收入",
            left: 'center',
            textStyle: {
              fontSize: 18,
              color: '#333'
            }
          },
          xAxis: {
            type: "category",
            data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            axisLabel: {
              color: '#666'
            }
          },
          yAxis: {
            type: "value",
            axisLabel: {
              color: '#666'
            }
          },
          series: [
            {
              data: [0, 0, 0, 0, 0, 0, 0],
              type: "bar",
              itemStyle: {
                color: '#409EFF'
              }
            },
          ],
        };
        barChart.setOption(barOption);
      });
    },

    // 初始化饼图
    initPieChart() {
      this.$nextTick(() => {
        const pieChart = echarts.init(document.getElementById("pie"));
        const pieOption = {
          title: {
            text: '收入结构饼图',
            left: 'center',
            textStyle: {
              fontSize: 18,
              color: '#333'
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 'left',
          },
          series: [
            {
              name: '收入来源',
              type: 'pie',
              radius: '50%',
              data: [
                { value: 1048, name: '男装' },
                { value: 735, name: '女装' },
                { value: 580, name: '童装' },
                { value: 484, name: '白酒' },
                { value: 300, name: '其他' }
              ],
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        };
        pieChart.setOption(pieOption);
      });
    },

    // 初始化本周收入折线图
    initWeekLineChart() {
      this.$nextTick(() => {
        const weekChart = echarts.init(document.getElementById("weekLine"));
        const weekOption = {
          title: {
            text: '本月收入',
            left: 'center',
            textStyle: {
              fontSize: 18,
              color: '#333'
            }
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: ['09-01', '09-03', '09-05', '09-07', '09-09', '09-11', '09-13', '09-15', '09-17', '09-19', '09-21', '09-23', '09-25', '09-27', '09-29'],
            axisLabel: {
              color: '#666'
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              color: '#666'
            }
          },
          series: [{
            data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            type: 'line',
            smooth: true,
            itemStyle: {
              color: '#67C23A'
            }
          }]
        };
        weekChart.setOption(weekOption);
      });
    },

    // 初始化本月收入折线图
    initMonthLineChart() {
      this.$nextTick(() => {
        const monthChart = echarts.init(document.getElementById("monthLine"));
        const monthOption = {
          title: {
            text: '本月收入',
            left: 'center',
            textStyle: {
              fontSize: 18,
              color: '#333'
            }
          },
          tooltip: {
            trigger: 'axis'
          },
          xAxis: {
            type: 'category',
            data: ['09-01', '09-03', '09-05', '09-07', '09-09', '09-11', '09-13', '09-15', '09-17', '09-19', '09-21', '09-23', '09-25', '09-27', '09-29'],
            axisLabel: {
              color: '#666'
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              color: '#666'
            }
          },
          series: [{
            data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            type: 'line',
            smooth: true,
            itemStyle: {
              color: '#E6A23C'
            }
          }]
        };
        monthChart.setOption(monthOption);
      });
    },

    // 商品类别柱状图数据
    async getGoodsChart() {
      // 使用模拟数据，因为后端接口可能不可用
      this.totalAll = 10000;
      this.totalWeek = 3500;
      this.totalMonth = 8500;
      this.total = this.totalAll;

      // 初始化默认图表
      this.initBarChart();
    },
  },

  // 生命周期 - 创建完成
  created() {
    //
  },

  // 生命周期 - 挂载完成
  mounted() {
    //

    this.getGoodsChart();
  },

  // 生命周期 - 更新之前
  beforeUpdate() {},

  // 生命周期 - 更新之后
  updated() {},

  // 生命周期 - 卸载前
  beforeUnmount() {
    // 替代 beforeDestroy
    //
  },

  // 生命周期 - 卸载后
  unmounted() {
    // 替代 destroyed
    //
  },

  // keep-alive 缓存组件激活时触发
  activated() {},

  // keep-alive 缓存组件失活时触发
  deactivated() {},
};
</script>

<style scoped>
</style>