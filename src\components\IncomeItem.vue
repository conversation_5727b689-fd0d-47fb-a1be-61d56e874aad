<!--
 * @Description: 
 * @Author: <PERSON><PERSON>
 * @Date: 2023-03-26 15:27:05
-->
<template>
  <div>
    <div class="head-cart" style="font-size: 22px; padding: 8px;background-color: lightgrey;">
      <i class="iconfont icon-r-shield" style="font-size: 26px;"></i>
       销量第{{index}}
    </div>
    <div class="body">
      <div style="display: inline-block;margin-left: 40px">
        <router-link :to="'/goodview/'+goods.id">
          <img :src="goods.img" style="width: 120px;height:120px">
        </router-link>
      </div>
      <div style="display: inline-block;line-height: 40px;padding: 20px" >
        <div class="item-header">
          <div class="header-cell">商品id</div>
          <div class="header-cell name-header">商品名称</div>
          <div class="header-cell">销售额</div>
        </div>
        <div class="item-content">
          <div class="content-cell">{{goods.id}}</div>
          <div class="content-cell name-cell">
            <router-link :to="'/goodview/'+goods.id">
              {{goods.name}}
            </router-link>
          </div>
          <div class="content-cell"><b>￥{{goods.amount}}</b></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: "IncomeItem",
  props:{
    goods: Object,
    index: Number,
    categories: Array
  },
  inject: ['store'],
  data() {
    return {
      baseApi: this.store.baseApi,
    }
  },
  mounted() {
    console.log('IncomeItem goods prop:', this.goods);
  }
}
</script>

<style scoped>
.header{
  padding: 10px;
  color: black;
  font-size: 20px;
  font-weight: bolder;
  border: black 1px solid;
}
.body{
  background-color: #daf3ff;
  padding: 5px;
}
.item-header {
  display: flex;
  border-bottom: 1px solid #ddd;
  padding-bottom: 5px;
  margin-bottom: 5px;
}
.item-content {
  display: flex;
}
.header-cell {
  width: 120px;
  text-align: center;
  font-size: 18px;
  color: black;
  font-weight: normal;
}
.header-cell.name-header {
  width: 300px;
}
.content-cell {
  width: 120px;
  text-align: center;
  font-size: 20px;
  font-weight: lighter;
  padding: 5px 0;
}
.content-cell.name-cell {
  width: 300px;
}
.content-cell a {
  text-decoration: none;
  color: inherit;
}
.content-cell a:hover {
  text-decoration: underline;
}
</style>