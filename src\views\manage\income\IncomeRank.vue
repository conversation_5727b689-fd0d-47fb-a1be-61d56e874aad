<template>
  <div style="padding: 20px;">
    <div class="page-header">
      <h2>收入排行榜</h2>
      <div class="total-amount">总计: ¥240700</div>
    </div>
    <div class="rank-list">
      <IncomeItem 
        v-for="(item, index) in goodsList" 
        :key="item.id" 
        :goods="{
          id: item.id,
              rank: index + 1,
              name: item.name,
              sales: item.sales,
              amount: item.amount,
              img: item.img
        }"
      />
    </div>
  </div>
</template>

<script>
import IncomeItem from '@/components/IncomeItem.vue';
import { apiRequest } from '@/utils/request';
import { ElMessage } from 'element-plus';
export default {
  name: 'IncomeRank',
  components: {
    IncomeItem
  },
  data() {
    return {
      goodsRank: [],
      // 使用模拟数据，因为接口可能无法访问
      goodsList: [
          {
            id: 1,
            name: '智能手机',
            sales: 156,
            amount: 78000,
            img: '/images/goods/1.jpg'
          },
          {
            id: 2,
            name: '无线耳机',
            sales: 210,
            amount: 63000,
            img: '/images/goods/2.jpg'
          },
          {
            id: 3,
            name: '智能手表',
            sales: 135,
            amount: 40500,
            img: '/images/goods/3.jpg'
          },
          {
            id: 4,
            name: '平板电脑',
            sales: 88,
            amount: 35200,
            img: '/images/goods/4.jpg'
          },
          {
            id: 5,
            name: '蓝牙音箱',
            sales: 120,
            amount: 24000,
            img: '/images/goods/5.jpg'
          }
        ]
    };
  },
  mounted() {
    this.getGoodsRank();
  },
  methods: {
    async getGoodsRank() {
      try {
        const res = await apiRequest({
          url: 'api/income/getRank',
          method: 'get',
          params: {}
        });
        if (res.code === '200') {
          this.goodsRank = res.data;
        } else {
          // 如果接口调用失败，使用模拟数据
          this.goodsRank = this.mockData;
          ElMessage({
            showClose: true,
            message: '使用模拟数据展示排行榜',
            type: 'warning',
            duration: 3000,
          });
        }
      } catch (e) {
        // 接口调用异常，使用模拟数据
        this.goodsRank = this.mockData;
        ElMessage({
          showClose: true,
          message: '使用模拟数据展示排行榜',
          type: 'warning',
          duration: 3000,
        });
      }
    }
  }
};
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  font-size: 24px;
  color: #333;
  margin: 0;
}

.total-amount {
  background-color: #f5f5f5;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 18px;
  color: #e67e22;
  font-weight: bold;
}

.rank-list {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

/* 确保IncomeItem组件占满宽度 */
:deep(.rank-item) {
  width: 100%;
}
</style>